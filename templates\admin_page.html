<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - GigGenius</title>
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/logo.png') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --text-gray: #666;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: all 0.3s ease-in-out;
        }

        body {
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            overflow-x: hidden;
            background-color: #f8f9fa;
        }

        /* Navigation Styles */
        .navbar {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: white;
        }

        .logo img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
        }

        .logo h1 {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: rgba(255,255,255,0.2);
        }

        .profile-dropdown {
            position: relative;
        }

        .profile-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            cursor: pointer;
        }

        .profile-button img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Container and Layout */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .section h1 {
            color: var(--primary-blue);
            margin-bottom: 1.5rem;
            font-size: 2rem;
            font-weight: 600;
        }

        /* Tab Navigation */
        .tab-navigation {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            border-bottom: 2px solid #e9ecef;
        }

        .tab-btn {
            padding: 1rem 2rem;
            background: none;
            border: none;
            color: var(--text-gray);
            font-weight: 500;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            color: var(--primary-blue);
            border-bottom-color: var(--primary-blue);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-left: 4px solid var(--primary-blue);
        }

        .stat-card h3 {
            color: var(--text-gray);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-blue);
            margin-bottom: 0.5rem;
        }

        .stat-trend {
            color: #28a745;
            font-size: 0.9rem;
        }

        /* Table Styles */
        .table-responsive {
            overflow-x: auto;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .table th {
            background: var(--primary-blue);
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
            position: sticky;
            top: 0;
        }

        .table td {
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .profile-thumbnail {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        /* Button Styles */
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: var(--primary-blue);
            color: white;
        }

        .btn-primary:hover {
            background: #003a8c;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        /* Status Select */
        .status-select {
            padding: 0.5rem;
            border: 2px solid var(--primary-blue);
            border-radius: 6px;
            background: white;
            cursor: pointer;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .navbar {
                padding: 1rem;
            }

            .nav-links {
                display: none;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="logo">
            <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
            <h1>GigGenius Admin</h1>
        </div>
        <div class="nav-links">
            <a href="javascript:void(0)" onclick="showTab('dashboard')" class="active">Dashboard</a>
            <a href="javascript:void(0)" onclick="showTab('clients')">Clients</a>
            <a href="javascript:void(0)" onclick="showTab('geniuses')">Geniuses</a>
            <a href="javascript:void(0)" onclick="showTab('earnings')">Earnings</a>
        </div>
        <div class="profile-dropdown">
            <div class="profile-button">
                <img src="{{ admin.profile_picture_url }}" alt="Admin Profile">
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container">
        <!-- Tab Navigation -->
        <div class="tab-navigation">
            <button class="tab-btn active" onclick="showTab('dashboard')">Dashboard</button>
            <button class="tab-btn" onclick="showTab('clients')">Clients</button>
            <button class="tab-btn" onclick="showTab('geniuses')">Geniuses</button>
            <button class="tab-btn" onclick="showTab('earnings')">Earnings</button>
        </div>

        <!-- Dashboard Tab -->
        <div id="dashboard" class="tab-content active">
            <div class="section">
                <h1>Welcome to Admin Dashboard</h1>
                <div class="dashboard-grid">
                    <div class="stat-card">
                        <h3>Total Users</h3>
                        <p class="stat-number">{{ (register_geniuses|length + approve_geniuses|length + register_clients|length + approve_clients|length) }}</p>
                        <p class="stat-trend">↑ Active platform users</p>
                    </div>
                    <div class="stat-card">
                        <h3>Pending Approvals</h3>
                        <p class="stat-number">{{ (register_geniuses|length + register_clients|length) }}</p>
                        <p class="stat-trend">Awaiting review</p>
                    </div>
                    <div class="stat-card">
                        <h3>Total Earnings</h3>
                        <p class="stat-number">${{ total_earnings }}</p>
                        <p class="stat-trend">↑ Platform commission</p>
                    </div>
                    <div class="stat-card">
                        <h3>Monthly Revenue</h3>
                        <p class="stat-number">${{ monthly_revenue }}</p>
                        <p class="stat-trend">↑ This month</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Clients Tab -->
        <div id="clients" class="tab-content">
            <div class="section">
                <h1>Client Management</h1>

                <!-- Pending Clients -->
                <h3>Pending Approvals ({{ register_clients|length }})</h3>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Profile</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Business</th>
                                <th>Position</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for client in register_clients %}
                            <tr>
                                <td>{{ client.id }}</td>
                                <td>
                                    {% if client.profile_photo %}
                                        <img src="{{ client.profile_photo }}" alt="Profile" class="profile-thumbnail">
                                    {% else %}
                                        <img src="{{ url_for('static', filename='img/default_profile.png') }}" alt="Default" class="profile-thumbnail">
                                    {% endif %}
                                </td>
                                <td>{{ client.first_name }} {{ client.last_name }}</td>
                                <td>{{ client.work_email }}</td>
                                <td>{{ client.business_name }}</td>
                                <td>{{ client.position }}</td>
                                <td>{{ client.created_at.strftime('%Y-%m-%d') if client.created_at else 'N/A' }}</td>
                                <td>
                                    <select class="status-select" onchange="updateClientStatus({{ client.id }}, this.value)">
                                        <option value="">Select Action</option>
                                        <option value="approved">Approve</option>
                                        <option value="declined">Decline</option>
                                        <option value="deleted">Delete</option>
                                    </select>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Approved Clients -->
                <h3 style="margin-top: 2rem;">Approved Clients ({{ approve_clients|length }})</h3>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Profile</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Business</th>
                                <th>Position</th>
                                <th>Country</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for client in approve_clients %}
                            <tr>
                                <td>{{ client.id }}</td>
                                <td>
                                    {% if client.profile_photo %}
                                        <img src="{{ client.profile_photo }}" alt="Profile" class="profile-thumbnail">
                                    {% else %}
                                        <img src="{{ url_for('static', filename='img/default_profile.png') }}" alt="Default" class="profile-thumbnail">
                                    {% endif %}
                                </td>
                                <td>{{ client.first_name }} {{ client.last_name }}</td>
                                <td>{{ client.work_email }}</td>
                                <td>{{ client.business_name }}</td>
                                <td>{{ client.position }}</td>
                                <td>{{ client.country }}</td>
                                <td>
                                    <button class="btn btn-danger btn-sm" onclick="deleteClient({{ client.id }})">Delete</button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Geniuses Tab -->
        <div id="geniuses" class="tab-content">
            <div class="section">
                <h1>Genius Management</h1>

                <!-- Pending Geniuses -->
                <h3>Pending Approvals ({{ register_geniuses|length }})</h3>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Profile</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Position</th>
                                <th>Country</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for genius in register_geniuses %}
                            <tr>
                                <td>{{ genius.id }}</td>
                                <td>
                                    {% if genius.profile_photo %}
                                        <img src="{{ genius.profile_photo }}" alt="Profile" class="profile-thumbnail">
                                    {% else %}
                                        <img src="{{ url_for('static', filename='img/default_profile.png') }}" alt="Default" class="profile-thumbnail">
                                    {% endif %}
                                </td>
                                <td>{{ genius.first_name }} {{ genius.last_name }}</td>
                                <td>{{ genius.email }}</td>
                                <td>{{ genius.position }}</td>
                                <td>{{ genius.country }}</td>
                                <td>{{ genius.created_at.strftime('%Y-%m-%d') if genius.created_at else 'N/A' }}</td>
                                <td>
                                    <select class="status-select" onchange="updateGeniusStatus({{ genius.id }}, this.value)">
                                        <option value="">Select Action</option>
                                        <option value="approved">Approve</option>
                                        <option value="declined">Decline</option>
                                        <option value="deleted">Delete</option>
                                    </select>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Earnings Tab -->
        <div id="earnings" class="tab-content">
            <div class="section">
                <h1>Platform Earnings</h1>
                <div class="dashboard-grid">
                    <div class="stat-card">
                        <h3>Total Earnings</h3>
                        <p class="stat-number">${{ total_earnings }}</p>
                        <p class="stat-trend">↑ All-time platform commission</p>
                    </div>
                    <div class="stat-card">
                        <h3>Monthly Revenue</h3>
                        <p class="stat-number">${{ monthly_revenue }}</p>
                        <p class="stat-trend">↑ Current month earnings</p>
                    </div>
                    <div class="stat-card">
                        <h3>Completed Transactions</h3>
                        <p class="stat-number">{{ completed_transactions }}</p>
                        <p class="stat-trend">Total completed jobs</p>
                    </div>
                    <div class="stat-card">
                        <h3>Average Commission</h3>
                        <p class="stat-number">10%</p>
                        <p class="stat-trend">Standard platform rate</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Tab switching functionality
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all tab buttons
            const tabBtns = document.querySelectorAll('.tab-btn');
            tabBtns.forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked button
            event.target.classList.add('active');

            // Update nav links
            const navLinks = document.querySelectorAll('.nav-links a');
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.onclick && link.onclick.toString().includes(tabName)) {
                    link.classList.add('active');
                }
            });
        }

        // Status update functions
        function updateClientStatus(clientId, status) {
            if (!status) return;

            fetch('/update_client_status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    client_id: clientId,
                    status: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred');
            });
        }

        function updateGeniusStatus(geniusId, status) {
            if (!status) return;

            fetch('/update_genius_status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    genius_id: geniusId,
                    status: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred');
            });
        }

        function deleteClient(clientId) {
            if (confirm('Are you sure you want to delete this client?')) {
                fetch('/delete_client', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        client_id: clientId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred');
                });
            }
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            showTab('dashboard');
        });
    </script>
</body>
</html>
